# 传感器工具箱新功能使用说明

## 功能概述

本次更新为传感器工具箱添加了两个新功能：

1. **车型选择界面** - 启动时的虚拟车型选择
2. **基础检查表格显示** - 点击基础检查卡片显示详细表格

## 功能详情

### 1. 车型选择界面

#### 功能描述
- 工具箱启动时会首先显示车型选择界面
- 提供三种车型选择：宝马X6、奥迪A8、长安深蓝SL03
- 通过下拉菜单方式选择车型
- 无论选择哪种车型，都能正常启动工具箱

#### 使用方法
1. 运行 `main.py` 启动程序
2. 在弹出的车型选择对话框中，从下拉菜单选择车型
3. 点击"确认启动"按钮进入工具箱主界面
4. 点击"取消"按钮退出程序

#### 技术实现
- 在 `main.py` 中添加了 `VehicleSelectionDialog` 类
- 使用 PyQt5 的 QDialog 和 QComboBox 组件
- 集成到主程序启动流程中

### 2. 基础检查表格显示

#### 功能描述
- 进入基础检查分类时，界面会自动显示一个空白的4x4表格
- 表格第一行固定显示：目标、国标、内容、注意
- 其余行初始显示"点击上方卡片查看详情"
- 单击任意基础检查卡片时，表格会填充该项目的详细信息
- 表格支持滚动查看，具有良好的视觉效果

#### 使用方法
1. 启动工具箱并选择车型
2. 在左侧导航栏点击"基础检查"分类
3. 界面会显示基础检查卡片和一个空白表格
4. 单击任意基础检查项目卡片（如"安全帽"）
5. 表格会自动填充该项目的详细检查信息
6. 表格会自动滚动到可见区域

#### 数据配置
表格数据来源于 `apps_config.json` 文件中的配置：

```json
{
    "基础检查": [
        {
            "name": "安全帽",
            "icon": "../image/OTA1.png",
            "description1": "[安全帽],[安全帽],[安全帽],[安全帽],",
            "description2": "[安全帽],[安全帽],[安全帽],[安全帽],",
            "description3": "[安全帽],[安全帽],[安全帽],[安全帽],",
            "description4": "[安全帽],[安全帽],[安全帽],[安全帽],"
        }
    ]
}
```

- `description1` 到 `description4` 分别对应表格的第2-4行数据
- 每个 description 字段使用逗号分隔4个值，对应表格的4列
- 方括号会被自动移除以保持表格整洁

#### 技术实现
- 在 `AppFrame` 类中重写了 `handle_single_click` 方法
- 在 `MainWindow` 类中添加了 `show_basic_check_table` 方法
- 使用 PyQt5 的 QTableWidget 组件创建表格
- 实现了表格的自动清理和滚动功能

## 文件修改说明

### 修改的文件

1. **main.py**
   - 添加了车型选择对话框类 `VehicleSelectionDialog`
   - 修改了主函数启动流程
   - 增加了必要的 PyQt5 组件导入

2. **main_window.py**
   - 在 `AppFrame` 类中重写了 `handle_single_click` 方法
   - 添加了 `show_basic_check_table` 方法及相关辅助方法
   - 增加了 QTableWidget 相关组件的导入

3. **apps_config.json**
   - 已包含基础检查项目的 description 字段配置
   - 支持表格数据的动态加载

### 新增的文件

1. **test_features.py** - 功能测试脚本
2. **新功能使用说明.md** - 本说明文档

## 测试验证

运行 `test_features.py` 可以验证新功能的正确性：

```bash
python test_features.py
```

测试内容包括：
- 车型选择对话框的创建和功能
- 基础检查配置的正确性
- 主窗口集成的完整性

## 注意事项

1. 车型选择目前为虚拟功能，选择不同车型不会影响工具箱的实际功能
2. 基础检查表格仅在"基础检查"分类中生效
3. 表格数据依赖于 `apps_config.json` 文件的正确配置
4. 建议在修改配置文件前先备份原文件
5. **重要**：基础检查项目必须包含 `path` 字段（可以是虚拟值如 "basic_check_item"），否则会被系统过滤掉
6. 如果基础检查界面显示为空白，请检查缓存文件并删除 `cache/apps_data.cache` 文件

## 后续扩展建议

1. 可以为不同车型添加特定的功能配置
2. 可以扩展表格显示功能到其他分类
3. 可以添加表格数据的编辑功能
4. 可以增加更多的车型选项
