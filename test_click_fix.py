#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基础检查点击修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_check_click():
    """测试基础检查点击功能"""
    print("=== 测试基础检查点击功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 选择基础检查分类
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            if item.text() == "基础检查":
                window.category_list.setCurrentItem(item)
                window.on_category_selected(item)
                print("✓ 选择基础检查分类")
                break
        
        # 等待界面更新
        QTest.qWait(1000)
        app.processEvents()
        
        # 查找基础检查卡片
        from main_window import AppFrame
        app_frames = window.scroll_content.findChildren(AppFrame)
        
        basic_check_frame = None
        for frame in app_frames:
            if hasattr(frame, 'app_data') and frame.app_data.get('name') == '安全帽':
                basic_check_frame = frame
                break
        
        if basic_check_frame:
            print("✓ 找到安全帽卡片")
            
            # 检查当前分类设置
            print(f"当前分类: {getattr(window, 'current_category', '未设置')}")
            
            # 模拟单击事件
            print("测试单击事件...")
            try:
                basic_check_frame.handle_single_click()
                print("✓ 单击事件处理成功")
            except Exception as e:
                print(f"✗ 单击事件处理失败: {e}")
            
            # 模拟双击事件
            print("测试双击事件...")
            try:
                # 创建模拟双击事件
                from PyQt5.QtCore import QEvent
                from PyQt5.QtGui import QMouseEvent
                
                double_click_event = QMouseEvent(
                    QEvent.MouseButtonDblClick,
                    basic_check_frame.rect().center(),
                    Qt.LeftButton,
                    Qt.LeftButton,
                    Qt.NoModifier
                )
                
                # 发送事件到事件过滤器
                basic_check_frame.eventFilter(basic_check_frame, double_click_event)
                print("✓ 双击事件处理成功")
                
            except Exception as e:
                print(f"✗ 双击事件处理失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 检查表格是否存在和更新
            from PyQt5.QtWidgets import QTableWidget
            table = window.scroll_content.findChild(QTableWidget, "basic_check_table")
            if table:
                print("✓ 找到基础检查表格")
                
                # 检查表格内容
                title_item = table.item(0, 0)
                if title_item and title_item.text() == "目标":
                    print("✓ 表格标题正确")
                
                # 检查是否有数据填充
                data_item = table.item(1, 0)
                if data_item and data_item.text() != "点击上方卡片查看详情":
                    print("✓ 表格数据已填充")
                else:
                    print("? 表格数据未填充或仍为默认值")
            else:
                print("✗ 未找到基础检查表格")
        else:
            print("✗ 未找到安全帽卡片")
        
        print("\n测试完成!")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试基础检查点击修复...\n")
    
    success = test_basic_check_click()
    
    if success:
        print("\n✓ 测试完成!")
    else:
        print("\n✗ 测试失败!")

if __name__ == "__main__":
    main()
