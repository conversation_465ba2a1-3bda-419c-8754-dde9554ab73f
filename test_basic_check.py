#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基础检查界面显示的脚本
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置加载"""
    print("测试配置加载...")
    
    try:
        with open("apps_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        basic_check = config.get("基础检查", [])
        print(f"✓ 基础检查项目数量: {len(basic_check)}")
        
        for i, item in enumerate(basic_check):
            print(f"  项目 {i+1}: {item.get('name', '未知')}")
            print(f"    description: {item.get('description', '无')}")
            print(f"    description1: {item.get('description1', '无')}")
            print(f"    description2: {item.get('description2', '无')}")
            print(f"    description3: {item.get('description3', '无')}")
            print()
            
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False

def test_main_window_basic_check():
    """测试主窗口基础检查功能"""
    print("测试主窗口基础检查功能...")
    
    try:
        from main_window import MainWindow
        app = QApplication(sys.argv)
        
        window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 检查配置数据
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"✓ 主窗口加载的基础检查项目数量: {len(basic_check_data)}")
        
        if len(basic_check_data) > 0:
            first_item = basic_check_data[0]
            print(f"✓ 第一个项目: {first_item.get('name', '未知')}")
            
            # 测试表格显示功能
            if hasattr(window, 'show_basic_check_table'):
                print("✓ show_basic_check_table方法存在")
                
                # 模拟调用表格显示
                try:
                    window.show_basic_check_table(first_item)
                    print("✓ 表格显示功能调用成功")
                except Exception as e:
                    print(f"✗ 表格显示功能调用失败: {e}")
                    return False
            else:
                print("✗ show_basic_check_table方法不存在")
                return False
        else:
            print("✗ 没有基础检查项目数据")
            return False
            
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ 主窗口测试失败: {e}")
        return False

def test_app_frame_integration():
    """测试AppFrame集成"""
    print("测试AppFrame集成...")
    
    try:
        from main_window import AppFrame, MainWindow
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        window.current_category = "基础检查"  # 设置当前分类
        
        # 获取基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        if len(basic_check_data) > 0:
            first_item = basic_check_data[0]
            
            # 创建AppFrame
            app_frame = AppFrame(first_item, None, window)
            print("✓ AppFrame创建成功")
            
            # 测试单击处理
            if hasattr(app_frame, 'handle_single_click'):
                print("✓ handle_single_click方法存在")
                
                try:
                    app_frame.handle_single_click()
                    print("✓ 单击处理功能调用成功")
                except Exception as e:
                    print(f"✗ 单击处理功能调用失败: {e}")
                    return False
            else:
                print("✗ handle_single_click方法不存在")
                return False
        else:
            print("✗ 没有基础检查项目数据")
            return False
            
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ AppFrame集成测试失败: {e}")
        return False

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("基础检查功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        test_btn = QPushButton("测试基础检查表格显示")
        test_btn.clicked.connect(self.test_table_display)
        layout.addWidget(test_btn)
        
        # 导入主窗口
        from main_window import MainWindow
        self.main_window = MainWindow()
        
    def test_table_display(self):
        """测试表格显示"""
        try:
            # 获取基础检查数据
            basic_check_data = self.main_window.apps_data.get("基础检查", [])
            if len(basic_check_data) > 0:
                first_item = basic_check_data[0]
                print(f"测试项目: {first_item.get('name', '未知')}")
                
                # 调用表格显示
                self.main_window.show_basic_check_table(first_item)
                print("✓ 表格显示成功")
            else:
                print("✗ 没有基础检查数据")
        except Exception as e:
            print(f"✗ 表格显示失败: {e}")

def run_interactive_test():
    """运行交互式测试"""
    print("运行交互式测试...")
    
    app = QApplication(sys.argv)
    
    test_window = TestWindow()
    test_window.show()
    
    print("✓ 测试窗口已打开，点击按钮测试功能")
    
    app.exec_()

def main():
    """主测试函数"""
    print("开始基础检查功能测试...\n")
    
    results = []
    results.append(test_config_loading())
    results.append(test_main_window_basic_check())
    results.append(test_app_frame_integration())
    
    print(f"\n自动测试完成!")
    print(f"成功: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ 所有自动测试通过!")
        print("\n启动交互式测试...")
        run_interactive_test()
    else:
        print("✗ 部分自动测试失败，请检查实现")

if __name__ == "__main__":
    main()
