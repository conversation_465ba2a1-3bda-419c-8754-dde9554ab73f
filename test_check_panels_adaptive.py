#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础检查面板自适应功能测试脚本
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加主程序路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class CheckPanelsTestWindow(QWidget):
    """基础检查面板自适应测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.test_results = []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("基础检查面板自适应功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("基础检查面板自适应功能测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # 说明
        info_text = """
🔧 测试内容：
• 检查项目面板的自适应宽度调整
• 检查细则面板的自适应宽度调整  
• 相关标准面板的自适应宽度调整
• 窗口状态切换时的响应

📋 测试步骤：
1. 启动主程序并打开基础检查功能
2. 切换窗口状态（最大化/恢复）
3. 检查面板宽度是否正确调整

📏 自适应规则：
• 恢复窗口：检查项目(450/180), 检查细则(600/650), 相关标准(350/400)
• 最大化窗口：检查项目(600/250), 检查细则(850/900), 相关标准(500/550)
        """
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Microsoft YaHei", 11))
        info_label.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_test_btn = QPushButton("🚀 启动测试")
        self.start_test_btn.clicked.connect(self.start_test)
        self.start_test_btn.setFont(QFont("Microsoft YaHei", 12))
        self.start_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.start_test_btn)
        
        self.open_check_btn = QPushButton("📋 打开基础检查")
        self.open_check_btn.clicked.connect(self.open_basic_check)
        self.open_check_btn.setEnabled(False)
        self.open_check_btn.setFont(QFont("Microsoft YaHei", 12))
        self.open_check_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.open_check_btn)
        
        self.toggle_window_btn = QPushButton("🔄 切换窗口状态")
        self.toggle_window_btn.clicked.connect(self.toggle_window_state)
        self.toggle_window_btn.setEnabled(False)
        self.toggle_window_btn.setFont(QFont("Microsoft YaHei", 12))
        self.toggle_window_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: black;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E0A800;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.toggle_window_btn)
        
        self.check_adaptive_btn = QPushButton("🔍 检查自适应")
        self.check_adaptive_btn.clicked.connect(self.check_adaptive_sizes)
        self.check_adaptive_btn.setEnabled(False)
        self.check_adaptive_btn.setFont(QFont("Microsoft YaHei", 12))
        self.check_adaptive_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.check_adaptive_btn)
        
        layout.addLayout(button_layout)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
        
    def start_test(self):
        """开始测试"""
        try:
            self.log("开始启动主窗口...")
            self.main_window = MainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTimer.singleShot(3000, self.enable_test_buttons)
            
            self.log("主窗口已启动，等待3秒后开始测试...")
            self.start_test_btn.setEnabled(False)
            
        except Exception as e:
            self.log(f"启动主窗口失败: {e}")
            
    def enable_test_buttons(self):
        """启用测试按钮"""
        self.open_check_btn.setEnabled(True)
        self.toggle_window_btn.setEnabled(True)
        self.check_adaptive_btn.setEnabled(True)
        
        self.log("主窗口已就绪，可以开始测试")
        
    def open_basic_check(self):
        """打开基础检查功能"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 模拟点击基础检查
            self.main_window.show_basic_check_page()
            
            # 等待页面加载后检查
            QTimer.singleShot(1000, self.check_panels_loaded)
            
            self.log("正在打开基础检查功能...")
            
        except Exception as e:
            self.log(f"打开基础检查功能失败: {e}")
            
    def check_panels_loaded(self):
        """检查面板是否加载"""
        try:
            # 检查是否有检查面板
            if hasattr(self.main_window, 'scroll_content'):
                check_items_panels = self.main_window.scroll_content.findChildren(QWidget, "check_items_panel")
                check_content_panels = self.main_window.scroll_content.findChildren(QWidget, "check_content_panel")
                check_standards_panels = self.main_window.scroll_content.findChildren(QWidget, "check_standards_panel")
                
                if check_items_panels or check_content_panels or check_standards_panels:
                    self.log("✓ 基础检查面板已加载")
                    self.log(f"  - 检查项目面板: {len(check_items_panels)} 个")
                    self.log(f"  - 检查细则面板: {len(check_content_panels)} 个")
                    self.log(f"  - 相关标准面板: {len(check_standards_panels)} 个")
                    
                    # 自动检查一次自适应尺寸
                    self.check_adaptive_sizes()
                else:
                    self.log("❌ 基础检查面板未找到")
            else:
                self.log("❌ scroll_content 未找到")
                
        except Exception as e:
            self.log(f"检查面板加载失败: {e}")
            
    def toggle_window_state(self):
        """切换窗口状态"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 使用主窗口的切换方法
            self.main_window.toggle_window_size()
            
            # 等待状态更新后检查
            QTimer.singleShot(1000, self.check_adaptive_sizes)
            
            self.log("窗口状态已切换，等待检查自适应...")
            
        except Exception as e:
            self.log(f"切换窗口状态失败: {e}")
            
    def check_adaptive_sizes(self):
        """检查自适应尺寸"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 检查窗口状态
            is_maximized = self.main_window.isMaximized()
            window_state = "最大化" if is_maximized else "恢复窗口"
            
            self.log(f"\n=== 自适应尺寸检查 ===")
            self.log(f"窗口状态: {window_state}")
            
            if hasattr(self.main_window, 'scroll_content'):
                # 检查各类面板
                self._check_panel_sizes("check_items_panel", "检查项目面板", 
                                      (600, 250) if is_maximized else (450, 180))
                self._check_panel_sizes("check_content_panel", "检查细则面板", 
                                      (850, 900) if is_maximized else (600, 650))
                self._check_panel_sizes("check_standards_panel", "相关标准面板", 
                                      (500, 550) if is_maximized else (350, 400))
            else:
                self.log("❌ scroll_content 未找到")
                
        except Exception as e:
            self.log(f"检查自适应尺寸失败: {e}")
            
    def _check_panel_sizes(self, object_name, panel_name, expected_sizes):
        """检查特定面板的尺寸"""
        panels = self.main_window.scroll_content.findChildren(QWidget, object_name)
        expected_max, expected_min = expected_sizes
        
        if panels:
            for i, panel in enumerate(panels):
                max_width = panel.maximumWidth()
                min_width = panel.minimumWidth()
                
                max_correct = max_width == expected_max
                min_correct = min_width == expected_min
                
                self.log(f"{panel_name}[{i}]: 最大宽度 {max_width} (期望: {expected_max}) {'✓' if max_correct else '❌'}")
                self.log(f"{panel_name}[{i}]: 最小宽度 {min_width} (期望: {expected_min}) {'✓' if min_correct else '❌'}")
        else:
            self.log(f"❌ {panel_name} 未找到")
            
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.result_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.result_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    test_window = CheckPanelsTestWindow()
    test_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
