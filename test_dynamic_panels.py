#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态三栏组功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dynamic_panels():
    """测试动态三栏组功能"""
    print("=== 测试动态三栏组功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面初始化
        QTest.qWait(1000)
        
        # 设置当前分类为基础检查
        window.current_category = "基础检查"
        window.display_single_category("基础检查")

        print("✓ 切换到基础检查分类")
        
        # 等待界面更新
        QTest.qWait(1000)
        
        # 检查基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"基础检查项目数量: {len(basic_check_data)}")
        
        if len(basic_check_data) > 0:
            # 测试第一个项目
            first_item = basic_check_data[0]
            print(f"测试项目: {first_item.get('name')}")
            
            # 显示项目数据
            window.show_basic_check_table(first_item)
            print("✓ 调用show_basic_check_table成功")
            
            # 等待面板创建
            QTest.qWait(1000)
            
            # 检查面板容器
            from PyQt5.QtWidgets import QWidget
            panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels_main")
            if panels_container:
                print("✓ 找到动态面板容器")
                
                # 检查子组件数量
                layout = panels_container.layout()
                if layout:
                    child_count = layout.count()
                    print(f"✓ 面板容器中有 {child_count} 个子组件")
                    
                    # 检查每个子组件
                    for i in range(child_count):
                        item = layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            print(f"  - 子组件 {i+1}: {widget.__class__.__name__}")
                else:
                    print("✗ 面板容器没有布局")
            else:
                print("✗ 未找到动态面板容器")
            
            # 测试其他项目
            if len(basic_check_data) > 1:
                print("\n测试第二个项目...")
                second_item = basic_check_data[1]
                print(f"测试项目: {second_item.get('name')}")
                
                window.show_basic_check_table(second_item)
                QTest.qWait(1000)
                
                # 再次检查面板
                panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels_main")
                if panels_container:
                    layout = panels_container.layout()
                    if layout:
                        child_count = layout.count()
                        print(f"✓ 更新后面板容器中有 {child_count} 个子组件")
        else:
            print("✗ 没有基础检查数据")
        
        print("\n测试完成!")
        
        # 保持窗口打开一段时间以便观察
        QTimer.singleShot(5000, app.quit)
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始动态三栏组功能测试...\n")
    
    success = test_dynamic_panels()
    
    if success:
        print("\n✓ 测试完成!")
    else:
        print("\n✗ 测试失败!")

if __name__ == "__main__":
    main()
