#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分类颜色系统
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_color_system():
    """测试分类颜色系统"""
    print("=== 测试分类颜色系统 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(1000)
        app.processEvents()
        
        # 检查分类颜色系统
        print("\n=== 检查分类颜色系统 ===")
        
        # 定义预期的分类分组
        expected_groups = {
            "核心检测功能": ["基础检查", "装配", "调试", "测试", "标定"],
            "扩展功能工具": ["便捷工具", "信息查询", "流程生成助手", "状态监控系统", "故障诊断系统"],
            "管理设置功能": ["社区", "日志管理", "主题设置", "首页"]
        }
        
        # 检查每个分类的颜色
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            category_name = item.text()
            
            # 获取背景颜色
            background = item.background()
            if background.color().alpha() > 0:
                color = background.color()
                print(f"✓ {category_name}: RGB({color.red()}, {color.green()}, {color.blue()}, {color.alpha()})")
                
                # 判断属于哪个分组
                group_found = False
                for group_name, categories in expected_groups.items():
                    if category_name in categories:
                        print(f"  └─ 属于: {group_name}")
                        group_found = True
                        break
                
                if not group_found:
                    print(f"  └─ ⚠️ 未分组")
            else:
                print(f"❌ {category_name}: 无背景颜色")
        
        # 测试选择不同分类的颜色变化
        print("\n=== 测试选择效果 ===")
        
        test_categories = ["基础检查", "便捷工具", "社区"]
        for category in test_categories:
            for i in range(window.category_list.count()):
                item = window.category_list.item(i)
                if item.text() == category:
                    print(f"选择分类: {category}")
                    window.category_list.setCurrentItem(item)
                    QTest.qWait(500)
                    app.processEvents()
                    
                    # 检查选中后的颜色
                    selected_color = item.background().color()
                    print(f"  选中后颜色: RGB({selected_color.red()}, {selected_color.green()}, {selected_color.blue()}, {selected_color.alpha()})")
                    break
        
        print("\n=== 颜色系统测试完成 ===")
        print("1. 核心检测功能 (蓝色系): 基础检查、装配、调试、测试、标定")
        print("2. 扩展功能工具 (绿色系): 便捷工具、信息查询、流程生成助手、状态监控系统、故障诊断系统")
        print("3. 管理设置功能 (紫色系): 社区、日志管理、主题设置、首页")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_color_system()
