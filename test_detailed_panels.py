#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试动态三栏组功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_detailed_panels():
    """详细测试动态三栏组功能"""
    print("=== 详细测试动态三栏组功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QGroupBox, QTextEdit
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面初始化
        QTest.qWait(1000)
        
        # 设置当前分类为基础检查
        window.current_category = "基础检查"
        window.display_single_category("基础检查")
        
        print("✓ 切换到基础检查分类")
        
        # 等待界面更新
        QTest.qWait(1000)
        
        # 检查基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"基础检查项目数量: {len(basic_check_data)}")
        
        if len(basic_check_data) > 0:
            # 测试第一个项目
            first_item = basic_check_data[0]
            print(f"\n测试项目: {first_item.get('name')}")
            print(f"项目描述: {first_item.get('description', '无描述')}")
            
            # 显示description字段
            for i in range(1, 10):  # 检查description1到description9
                desc_key = f"description{i}"
                desc_value = first_item.get(desc_key, "")
                if desc_value:
                    print(f"  {desc_key}: {desc_value}")
            
            # 显示项目数据
            window.show_basic_check_table(first_item)
            print("✓ 调用show_basic_check_table成功")
            
            # 等待面板创建
            QTest.qWait(1000)
            
            # 检查面板容器
            panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels_main")
            if panels_container:
                print("✓ 找到动态面板容器")
                
                # 检查子组件数量
                layout = panels_container.layout()
                if layout:
                    child_count = layout.count()
                    print(f"✓ 面板容器中有 {child_count} 个子组件")
                    
                    # 检查每个子组件的详细信息
                    for i in range(child_count):
                        item = layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            print(f"\n  === 子组件 {i+1} ===")
                            print(f"  类型: {widget.__class__.__name__}")
                            
                            # 查找标题标签
                            from PyQt5.QtWidgets import QLabel
                            labels = widget.findChildren(QLabel)
                            for label in labels:
                                if "检查项目组" in label.text():
                                    print(f"  标题: {label.text()}")
                                    break
                            
                            # 查找GroupBox
                            group_boxes = widget.findChildren(QGroupBox)
                            print(f"  包含 {len(group_boxes)} 个GroupBox:")
                            for j, group_box in enumerate(group_boxes):
                                print(f"    GroupBox {j+1}: {group_box.title()}")
                                
                                # 查找TextEdit
                                text_edits = group_box.findChildren(QTextEdit)
                                for text_edit in text_edits:
                                    content = text_edit.toPlainText()
                                    if content:
                                        print(f"      内容: {content[:50]}...")
                                    else:
                                        print(f"      内容: 空")
                else:
                    print("✗ 面板容器没有布局")
            else:
                print("✗ 未找到动态面板容器")
            
            # 测试其他项目
            if len(basic_check_data) > 1:
                print(f"\n=== 测试第二个项目 ===")
                second_item = basic_check_data[1]
                print(f"测试项目: {second_item.get('name')}")
                
                # 显示description字段
                for i in range(1, 10):  # 检查description1到description9
                    desc_key = f"description{i}"
                    desc_value = second_item.get(desc_key, "")
                    if desc_value:
                        print(f"  {desc_key}: {desc_value}")
                
                window.show_basic_check_table(second_item)
                QTest.qWait(1000)
                
                # 再次检查面板
                panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels_main")
                if panels_container:
                    layout = panels_container.layout()
                    if layout:
                        child_count = layout.count()
                        print(f"✓ 更新后面板容器中有 {child_count} 个子组件")
        else:
            print("✗ 没有基础检查数据")
        
        print("\n=== 测试完成 ===")
        
        # 保持窗口打开一段时间以便观察
        QTimer.singleShot(10000, app.quit)
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始详细动态三栏组功能测试...\n")
    
    success = test_detailed_panels()
    
    if success:
        print("\n✓ 详细测试完成!")
    else:
        print("\n✗ 详细测试失败!")

if __name__ == "__main__":
    main()
