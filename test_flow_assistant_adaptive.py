#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流程生成助手自适应功能测试脚本
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加主程序路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
    from chat_robot_widget import ChatRobotWidget
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class FlowAssistantTestWindow(QWidget):
    """流程生成助手自适应测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.test_results = []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("流程生成助手自适应功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("流程生成助手自适应功能测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # 说明
        info_text = """
🔧 测试内容：
• 流程生成助手的自适应尺寸调整
• 流程图高度自适应
• 面板宽度自适应
• 窗口状态切换时的响应

📋 测试步骤：
1. 启动主程序并打开流程生成助手
2. 切换窗口状态（最大化/恢复）
3. 检查组件尺寸是否正确调整
        """
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Microsoft YaHei", 11))
        info_label.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_test_btn = QPushButton("🚀 启动测试")
        self.start_test_btn.clicked.connect(self.start_test)
        self.start_test_btn.setFont(QFont("Microsoft YaHei", 12))
        self.start_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.start_test_btn)
        
        self.open_flow_btn = QPushButton("📋 打开流程助手")
        self.open_flow_btn.clicked.connect(self.open_flow_assistant)
        self.open_flow_btn.setEnabled(False)
        self.open_flow_btn.setFont(QFont("Microsoft YaHei", 12))
        self.open_flow_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.open_flow_btn)
        
        self.toggle_window_btn = QPushButton("🔄 切换窗口状态")
        self.toggle_window_btn.clicked.connect(self.toggle_window_state)
        self.toggle_window_btn.setEnabled(False)
        self.toggle_window_btn.setFont(QFont("Microsoft YaHei", 12))
        self.toggle_window_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: black;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E0A800;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.toggle_window_btn)
        
        self.check_adaptive_btn = QPushButton("🔍 检查自适应")
        self.check_adaptive_btn.clicked.connect(self.check_adaptive_sizes)
        self.check_adaptive_btn.setEnabled(False)
        self.check_adaptive_btn.setFont(QFont("Microsoft YaHei", 12))
        self.check_adaptive_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.check_adaptive_btn)
        
        layout.addLayout(button_layout)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
        
    def start_test(self):
        """开始测试"""
        try:
            self.log("开始启动主窗口...")
            self.main_window = MainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTimer.singleShot(3000, self.enable_test_buttons)
            
            self.log("主窗口已启动，等待3秒后开始测试...")
            self.start_test_btn.setEnabled(False)
            
        except Exception as e:
            self.log(f"启动主窗口失败: {e}")
            
    def enable_test_buttons(self):
        """启用测试按钮"""
        self.open_flow_btn.setEnabled(True)
        self.toggle_window_btn.setEnabled(True)
        self.check_adaptive_btn.setEnabled(True)
        
        self.log("主窗口已就绪，可以开始测试")
        
    def open_flow_assistant(self):
        """打开流程生成助手"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 模拟点击流程生成助手
            self.main_window.show_chat_robot_page()
            
            # 等待页面加载后检查
            QTimer.singleShot(1000, self.check_flow_assistant_loaded)
            
            self.log("正在打开流程生成助手...")
            
        except Exception as e:
            self.log(f"打开流程生成助手失败: {e}")
            
    def check_flow_assistant_loaded(self):
        """检查流程生成助手是否加载"""
        try:
            if hasattr(self.main_window, 'chat_robot_widget') and self.main_window.chat_robot_widget:
                self.log("✓ 流程生成助手已加载")
                
                # 检查自适应管理器是否设置
                if self.main_window.chat_robot_widget.adaptive_manager:
                    self.log("✓ 自适应管理器已设置")
                else:
                    self.log("❌ 自适应管理器未设置")
                    
                # 自动检查一次自适应尺寸
                self.check_adaptive_sizes()
            else:
                self.log("❌ 流程生成助手未加载")
                
        except Exception as e:
            self.log(f"检查流程生成助手失败: {e}")
            
    def toggle_window_state(self):
        """切换窗口状态"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 使用主窗口的切换方法
            self.main_window.toggle_window_size()
            
            # 等待状态更新后检查
            QTimer.singleShot(1000, self.check_adaptive_sizes)
            
            self.log("窗口状态已切换，等待检查自适应...")
            
        except Exception as e:
            self.log(f"切换窗口状态失败: {e}")
            
    def check_adaptive_sizes(self):
        """检查自适应尺寸"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 检查窗口状态
            is_maximized = self.main_window.isMaximized()
            window_state = "最大化" if is_maximized else "恢复窗口"
            
            self.log(f"\n=== 自适应尺寸检查 ===")
            self.log(f"窗口状态: {window_state}")
            
            # 检查流程生成助手
            if hasattr(self.main_window, 'chat_robot_widget') and self.main_window.chat_robot_widget:
                widget = self.main_window.chat_robot_widget
                
                # 检查流程图高度
                if hasattr(widget, 'flowchart'):
                    flowchart_height = widget.flowchart.maximumHeight()
                    expected_height = 350 if is_maximized else 280
                    height_correct = flowchart_height == expected_height
                    
                    self.log(f"流程图高度: {flowchart_height} (期望: {expected_height}) {'✓' if height_correct else '❌'}")
                
                # 检查面板宽度
                if hasattr(widget, 'skills_panel'):
                    panel_width = widget.skills_panel.maximumWidth()
                    expected_width = 420 if is_maximized else 350
                    width_correct = panel_width == expected_width
                    
                    self.log(f"技能面板宽度: {panel_width} (期望: {expected_width}) {'✓' if width_correct else '❌'}")
                
                # 检查自适应管理器状态
                if widget.adaptive_manager:
                    adaptive_state = widget.adaptive_manager.current_state
                    expected_state = "maximized" if is_maximized else "restored"
                    state_correct = adaptive_state == expected_state
                    
                    self.log(f"自适应状态: {adaptive_state} (期望: {expected_state}) {'✓' if state_correct else '❌'}")
                else:
                    self.log("❌ 自适应管理器未设置")
                    
            else:
                self.log("❌ 流程生成助手未找到")
                
        except Exception as e:
            self.log(f"检查自适应尺寸失败: {e}")
            
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.result_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.result_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    test_window = FlowAssistantTestWindow()
    test_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
