#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能的脚本
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_vehicle_selection():
    """测试车型选择功能"""
    print("测试车型选择功能...")
    
    try:
        from main import VehicleSelectionDialog
        app = QApplication(sys.argv)
        
        dialog = VehicleSelectionDialog()
        print("✓ 车型选择对话框创建成功")
        
        # 检查下拉框选项
        combo = dialog.vehicle_combo
        items = [combo.itemText(i) for i in range(combo.count())]
        expected_items = ["宝马X6", "奥迪A8", "长安深蓝SL03"]
        
        if items == expected_items:
            print("✓ 车型选项正确:", items)
        else:
            print("✗ 车型选项错误:", items, "期望:", expected_items)
        
        # 测试选择功能
        dialog.vehicle_combo.setCurrentIndex(1)  # 选择奥迪A8
        dialog.accept_selection()
        selected = dialog.get_selected_vehicle()
        
        if selected == "奥迪A8":
            print("✓ 车型选择功能正常")
        else:
            print("✗ 车型选择功能异常:", selected)
            
        app.quit()
        return True
        
    except Exception as e:
        print("✗ 车型选择功能测试失败:", str(e))
        return False

def test_basic_check_config():
    """测试基础检查配置"""
    print("\n测试基础检查配置...")
    
    try:
        # 检查apps_config.json文件
        if not os.path.exists("apps_config.json"):
            print("✗ apps_config.json文件不存在")
            return False
            
        with open("apps_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        if "基础检查" not in config:
            print("✗ 配置文件中没有'基础检查'分类")
            return False
            
        basic_check = config["基础检查"]
        if not isinstance(basic_check, list) or len(basic_check) == 0:
            print("✗ 基础检查配置为空")
            return False
            
        # 检查第一个项目（安全帽）的配置
        safety_hat = basic_check[0]
        required_fields = ["name", "description1", "description2", "description3"]
        
        for field in required_fields:
            if field not in safety_hat:
                print(f"✗ 安全帽配置缺少字段: {field}")
                return False
                
        print("✓ 基础检查配置正确")
        print(f"  - 项目数量: {len(basic_check)}")
        print(f"  - 第一个项目: {safety_hat['name']}")
        
        # 检查description字段格式
        desc1 = safety_hat.get("description1", "")
        if desc1 and "," in desc1:
            items = [item.strip() for item in desc1.split(',') if item.strip()]
            print(f"  - description1包含{len(items)}个项目")
        
        return True
        
    except Exception as e:
        print("✗ 基础检查配置测试失败:", str(e))
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    print("\n测试主窗口集成...")
    
    try:
        from main_window import MainWindow
        app = QApplication(sys.argv)
        
        window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 检查是否有show_basic_check_table方法
        if hasattr(window, 'show_basic_check_table'):
            print("✓ show_basic_check_table方法存在")
        else:
            print("✗ show_basic_check_table方法不存在")
            return False
            
        # 检查AppFrame类是否有handle_single_click方法
        from main_window import AppFrame
        if hasattr(AppFrame, 'handle_single_click'):
            print("✓ AppFrame.handle_single_click方法存在")
        else:
            print("✗ AppFrame.handle_single_click方法不存在")
            return False
            
        app.quit()
        return True
        
    except Exception as e:
        print("✗ 主窗口集成测试失败:", str(e))
        return False

def main():
    """主测试函数"""
    print("开始测试新增功能...\n")
    
    results = []
    results.append(test_vehicle_selection())
    results.append(test_basic_check_config())
    results.append(test_main_window_integration())
    
    print(f"\n测试完成!")
    print(f"成功: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ 所有功能测试通过!")
    else:
        print("✗ 部分功能测试失败，请检查实现")

if __name__ == "__main__":
    main()
