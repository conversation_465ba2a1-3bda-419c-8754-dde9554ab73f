#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flow import FlowWidget

class TestIntegrationWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("流程展示集成测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建按钮区域
        button_layout = QHBoxLayout()
        
        # 创建测试按钮 - 支持双参数
        btn_radar_assembly = QPushButton("毫米波雷达装配")
        btn_radar_assembly.clicked.connect(lambda: self.show_flow("装配", "毫米波雷达"))
        btn_radar_assembly.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        btn_radar_debug = QPushButton("毫米波雷达调试")
        btn_radar_debug.clicked.connect(lambda: self.show_flow("调试", "毫米波雷达"))
        btn_radar_debug.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)

        btn_lidar_calibration = QPushButton("激光雷达标定")
        btn_lidar_calibration.clicked.connect(lambda: self.show_flow("标定", "激光雷达"))
        btn_lidar_calibration.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
        """)

        btn_camera_test = QPushButton("摄像头测试")
        btn_camera_test.clicked.connect(lambda: self.show_flow("测试", "摄像头"))
        btn_camera_test.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        
        button_layout.addWidget(btn_radar_assembly)
        button_layout.addWidget(btn_radar_debug)
        button_layout.addWidget(btn_lidar_calibration)
        button_layout.addWidget(btn_camera_test)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

        # 创建流程展示区域
        self.flow_container = QWidget()
        main_layout.addWidget(self.flow_container)

        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #1e3a8a, stop: 0.5 #3730a3, stop: 1 #1e40af);
            }
        """)

        # 默认显示毫米波雷达装配流程
        self.show_flow("装配", "毫米波雷达")

    def show_flow(self, category, device_name):
        """显示指定的流程"""
        # 清空现有内容
        if self.flow_container.layout():
            while self.flow_container.layout().count():
                child = self.flow_container.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

        # 创建新的布局
        layout = QVBoxLayout(self.flow_container)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建流程组件，传入分类和设备名称
        flow_widget = FlowWidget(self, category, device_name)
        layout.addWidget(flow_widget)

        print(f"切换到流程: 分类={category}, 设备={device_name}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("流程展示集成测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestIntegrationWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
