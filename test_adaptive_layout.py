#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自适应布局功能
验证窗口状态切换时组件尺寸是否正确调整
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt, QTimer
from main_window import MainWindow, AppFrame, DraggableButton, FileExplorerFrame

def test_adaptive_layout():
    """测试自适应布局功能"""
    print("=== 测试自适应布局功能 ===")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 等待界面初始化
    QTest.qWait(1000)
    app.processEvents()
    
    print(f"✓ 主窗口已创建")
    print(f"  初始窗口状态: {'最大化' if window.is_maximized else '恢复窗口'}")
    print(f"  自适应管理器状态: {window.adaptive_manager.current_state}")
    print(f"  当前列数: {window.max_cols}")
    
    # 测试自适应管理器的基本功能
    print("\n--- 测试自适应管理器基本功能 ---")
    
    # 获取当前尺寸配置
    card_size = window.adaptive_manager.get_card_size()
    button_size = window.adaptive_manager.get_button_size()
    file_size = window.adaptive_manager.get_file_size()
    max_cols = window.adaptive_manager.get_max_cols()
    spacing = window.adaptive_manager.get_spacing()
    
    print(f"  卡片尺寸: {card_size}")
    print(f"  按钮尺寸: {button_size}")
    print(f"  文件框尺寸: {file_size}")
    print(f"  最大列数: {max_cols}")
    print(f"  间距: {spacing}")
    
    # 切换到某个分类以显示应用卡片
    print("\n--- 切换到智能检测分类 ---")
    if hasattr(window, 'category_list'):
        # 查找智能检测分类
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            if item and "智能检测" in item.text():
                window.category_list.setCurrentRow(i)
                window._on_category_clicked(item)
                break
    
    # 等待界面更新
    QTest.qWait(500)
    app.processEvents()
    
    # 查找并检查AppFrame组件
    app_frames = window.scroll_content.findChildren(AppFrame)
    print(f"  找到 {len(app_frames)} 个应用卡片")
    
    if app_frames:
        frame = app_frames[0]
        print(f"  第一个卡片尺寸: {frame.size().width()}x{frame.size().height()}")
        print(f"  卡片是否有自适应管理器: {frame.adaptive_manager is not None}")
        
        # 检查内部按钮
        if hasattr(frame, 'app_btn'):
            btn_size = frame.app_btn.size()
            print(f"  内部按钮尺寸: {btn_size.width()}x{btn_size.height()}")
            print(f"  按钮是否有自适应管理器: {frame.app_btn.adaptive_manager is not None}")
    
    # 测试窗口状态切换
    print("\n--- 测试窗口状态切换 ---")
    
    # 记录切换前的尺寸
    before_state = window.is_maximized
    before_cols = window.max_cols
    before_card_size = window.adaptive_manager.get_card_size()
    
    print(f"  切换前状态: {'最大化' if before_state else '恢复窗口'}")
    print(f"  切换前列数: {before_cols}")
    print(f"  切换前卡片尺寸: {before_card_size}")
    
    # 执行窗口状态切换
    window.toggle_window_size()
    
    # 等待切换完成
    QTest.qWait(1000)
    app.processEvents()
    
    # 记录切换后的尺寸
    after_state = window.is_maximized
    after_cols = window.max_cols
    after_card_size = window.adaptive_manager.get_card_size()
    
    print(f"  切换后状态: {'最大化' if after_state else '恢复窗口'}")
    print(f"  切换后列数: {after_cols}")
    print(f"  切换后卡片尺寸: {after_card_size}")
    
    # 验证状态是否正确切换
    if before_state != after_state:
        print("  ✓ 窗口状态切换成功")
    else:
        print("  ✗ 窗口状态切换失败")
    
    if before_cols != after_cols:
        print("  ✓ 列数调整成功")
    else:
        print("  ✗ 列数调整失败")
    
    if before_card_size != after_card_size:
        print("  ✓ 卡片尺寸调整成功")
    else:
        print("  ✗ 卡片尺寸调整失败")
    
    # 检查实际组件尺寸是否更新
    print("\n--- 检查组件尺寸更新 ---")
    app_frames_after = window.scroll_content.findChildren(AppFrame)
    if app_frames_after:
        frame_after = app_frames_after[0]
        actual_size = (frame_after.size().width(), frame_after.size().height())
        expected_size = window.adaptive_manager.get_card_size()
        
        print(f"  实际卡片尺寸: {actual_size}")
        print(f"  期望卡片尺寸: {expected_size}")
        
        if actual_size == expected_size:
            print("  ✓ 卡片尺寸更新正确")
        else:
            print("  ✗ 卡片尺寸更新不正确")
        
        # 检查按钮尺寸
        if hasattr(frame_after, 'app_btn'):
            btn_actual_size = (frame_after.app_btn.size().width(), frame_after.app_btn.size().height())
            btn_expected_size = window.adaptive_manager.get_button_size()
            
            print(f"  实际按钮尺寸: {btn_actual_size}")
            print(f"  期望按钮尺寸: {btn_expected_size}")
            
            if btn_actual_size == btn_expected_size:
                print("  ✓ 按钮尺寸更新正确")
            else:
                print("  ✗ 按钮尺寸更新不正确")
    
    # 再次切换回原状态进行验证
    print("\n--- 再次切换验证 ---")
    window.toggle_window_size()
    QTest.qWait(1000)
    app.processEvents()
    
    final_state = window.is_maximized
    final_cols = window.max_cols
    final_card_size = window.adaptive_manager.get_card_size()
    
    print(f"  最终状态: {'最大化' if final_state else '恢复窗口'}")
    print(f"  最终列数: {final_cols}")
    print(f"  最终卡片尺寸: {final_card_size}")
    
    # 验证是否回到初始状态
    if final_state == before_state and final_cols == before_cols and final_card_size == before_card_size:
        print("  ✓ 成功回到初始状态")
    else:
        print("  ✗ 未能正确回到初始状态")
    
    print("\n=== 自适应布局测试完成 ===")
    
    # 保持窗口打开一段时间供观察
    QTimer.singleShot(3000, app.quit)
    return app.exec_()

if __name__ == "__main__":
    test_adaptive_layout()
