#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除标题后的效果
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_no_title():
    """测试移除标题后的效果"""
    print("=== 测试移除标题后的效果 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面初始化
        QTest.qWait(1000)
        
        # 设置当前分类为基础检查
        window.current_category = "基础检查"
        window.display_single_category("基础检查")
        
        print("✓ 切换到基础检查分类")
        
        # 等待界面更新
        QTest.qWait(1000)
        
        # 检查基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        
        if len(basic_check_data) > 0:
            # 测试第一个项目
            first_item = basic_check_data[0]
            print(f"测试项目: {first_item.get('name')}")
            
            # 显示项目数据
            window.show_basic_check_table(first_item)
            print("✓ 调用show_basic_check_table成功")
            
            # 等待面板创建
            QTest.qWait(1000)
            
            # 检查面板容器
            panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels_main")
            if panels_container:
                print("✓ 找到动态面板容器")
                
                # 检查是否还有标题标签
                labels = panels_container.findChildren(QLabel)
                title_labels = []
                for label in labels:
                    text = label.text()
                    if "检查项目组" in text or "第" in text and "项" in text:
                        title_labels.append(text)
                
                if title_labels:
                    print(f"✗ 仍然找到标题标签: {title_labels}")
                else:
                    print("✓ 成功移除所有标题标签")
                
                # 检查子组件数量
                layout = panels_container.layout()
                if layout:
                    child_count = layout.count()
                    print(f"✓ 面板容器中有 {child_count} 个子组件")
                    
                    # 检查每个子组件是否直接包含GroupBox
                    for i in range(child_count):
                        item = layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            
                            # 查找GroupBox
                            from PyQt5.QtWidgets import QGroupBox
                            group_boxes = widget.findChildren(QGroupBox)
                            
                            # 查找标题标签
                            widget_labels = widget.findChildren(QLabel)
                            title_found = False
                            for label in widget_labels:
                                if "检查项目组" in label.text():
                                    title_found = True
                                    break
                            
                            if title_found:
                                print(f"  ✗ 子组件 {i+1} 仍有标题")
                            else:
                                print(f"  ✓ 子组件 {i+1} 无标题，直接包含 {len(group_boxes)} 个GroupBox")
            else:
                print("✗ 未找到动态面板容器")
        else:
            print("✗ 没有基础检查数据")
        
        print("\n=== 测试完成 ===")
        
        # 保持窗口打开一段时间以便观察
        QTimer.singleShot(5000, app.quit)
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试移除标题功能...\n")
    
    success = test_no_title()
    
    if success:
        print("\n✓ 标题移除测试完成!")
    else:
        print("\n✗ 标题移除测试失败!")

if __name__ == "__main__":
    main()
