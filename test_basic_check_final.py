#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终基础检查功能测试
"""

import sys
import os
import json

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

def test_basic_check_interface():
    """测试基础检查界面功能"""
    print("=== 基础检查界面功能测试 ===")
    
    try:
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 模拟选择基础检查分类
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            if item.text() == "基础检查":
                window.category_list.setCurrentItem(item)
                print("✓ 选择基础检查分类")
                # 手动触发分类选择事件
                window.on_category_selected(item)
                break

        # 等待界面更新
        QTest.qWait(1000)

        # 强制处理事件
        app.processEvents()
        
        # 检查是否有基础检查项目显示
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"✓ 基础检查项目数量: {len(basic_check_data)}")

        # 调试：检查当前分类
        print(f"当前分类: {getattr(window, 'current_category', '未设置')}")

        # 检查是否有空白表格
        from PyQt5.QtWidgets import QTableWidget
        table = window.scroll_content.findChild(QTableWidget, "basic_check_table")

        # 调试：列出所有子组件
        all_widgets = window.scroll_content.findChildren(QTableWidget)
        print(f"找到的所有表格组件数量: {len(all_widgets)}")
        for widget in all_widgets:
            print(f"  表格对象名: {widget.objectName()}")

        if table:
            print("✓ 找到基础检查表格")
            print(f"  表格行数: {table.rowCount()}")
            print(f"  表格列数: {table.columnCount()}")
            
            # 检查表格内容
            for row in range(table.rowCount()):
                row_content = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        row_content.append(item.text())
                    else:
                        row_content.append("空")
                print(f"  第{row+1}行: {row_content}")
        else:
            print("✗ 未找到基础检查表格")
        
        # 测试点击卡片功能
        print("\n测试点击卡片功能...")
        
        # 查找第一个基础检查卡片
        from main_window import AppFrame
        app_frames = window.scroll_content.findChildren(AppFrame)
        basic_check_frame = None
        
        for frame in app_frames:
            if hasattr(frame, 'app_data') and frame.app_data.get('name') == '安全帽':
                basic_check_frame = frame
                break
        
        if basic_check_frame:
            print("✓ 找到安全帽卡片")
            
            # 模拟单击事件
            basic_check_frame.handle_single_click()
            print("✓ 模拟单击安全帽卡片")
            
            # 等待表格更新
            QTest.qWait(200)
            
            # 检查表格是否更新
            if table:
                print("检查表格更新后的内容:")
                for row in range(table.rowCount()):
                    row_content = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        if item:
                            row_content.append(item.text())
                        else:
                            row_content.append("空")
                    print(f"  第{row+1}行: {row_content}")
        else:
            print("✗ 未找到安全帽卡片")
        
        print("\n测试完成!")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始基础检查界面功能测试...\n")
    
    success = test_basic_check_interface()
    
    if success:
        print("\n✓ 所有测试通过!")
    else:
        print("\n✗ 测试失败!")

if __name__ == "__main__":
    main()
