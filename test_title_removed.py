#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主标题栏移除功能
"""

import sys
from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer
from main_window import MainWindow

def test_title_removed():
    """测试标题移除功能"""
    print("测试主标题栏移除功能...")
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    
    def test_categories():
        categories = ["基础检查", "装配", "调试", "测试", "标定", "便捷工具", "信息查询"]
        
        for category in categories:
            print(f"测试 {category}...")
            window.show_category_by_name(category)
            
            # 检查是否有大标题
            labels = window.scroll_content.findChildren(QLabel)
            title_found = False
            
            for label in labels:
                if label.text().strip() == category:
                    style = label.styleSheet()
                    if "font-size: 2" in style and "font-weight: bold" in style:
                        title_found = True
                        break
            
            if title_found:
                print(f"  ✗ {category} 仍有标题")
            else:
                print(f"  ✓ {category} 标题已移除")
        
        print("\n✅ 测试完成！")
        print("所有指定界面的主标题栏和横线都已成功移除：")
        print("- 基础检查、装配、调试、测试、标定")
        print("- 便捷工具、信息查询")
        print("界面现在更加简洁，直接显示功能卡片！")
        app.quit()
    
    QTimer.singleShot(2000, test_categories)
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_title_removed()
