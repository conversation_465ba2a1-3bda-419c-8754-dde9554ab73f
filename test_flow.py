#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flow import FlowWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("流程展示测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建流程组件
        self.flow_widget = FlowWidget(self)
        layout.addWidget(self.flow_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #1e3a8a, stop: 0.5 #3730a3, stop: 1 #1e40af);
            }
        """)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("流程展示测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
