# 流程展示功能说明

## 功能概述

流程展示功能是一个嵌入在主窗口中的界面，用于展示操作流程的详细步骤。该功能根据 `tac.json` 文件的内容动态生成流程图和相关信息。

## 更新说明

**最新版本特性：**
- ✅ **取消了左上角的流程选择器**
- ✅ **通过点击卡片传参自动打开对应流程**
- ✅ **不再通过侧边栏打开，而是通过点击应用卡片**
- ✅ **箭头样式更加明显和美观**
- ✅ **支持参数化流程加载**

## 界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                Flow界面布局                                      │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                           │
│      左侧 1/4       │                    右侧 3/4                              │
│     ┌─────────┐     │  ┌─────────────────────────────────────────────────────┐ │
│     │ Step 1  │     │  │                                                     │ │
│     │ 确保调试│ ◄── │  │                   图片区域                          │ │
│     │ 操作安全│     │  │              (step的image路径)                      │ │
│     │ 规范    │     │  │                                                     │ │
│     └─────────┘     │  └─────────────────────────────────────────────────────┘ │
│          ↓          │  ┌─────────────────────────────────────────────────────┐ │
│     ┌─────────┐     │  │                                                     │ │
│     │ Step 2  │     │  │              文字解释区域 (2/3)                     │ │
│     │ 摆放角反│     │  │            (step的description内容)                  │ │
│     │ 射器或金│     │  │                                                     │ │
│     │ 属板    │     │  │                                                     │ │
│     └─────────┘     │  ├─────────────────────────────────────────────────────┤ │
│          ↓          │  │                                                     │ │
│     ┌─────────┐     │  │              标准区域 (1/3)                        │ │
│     │ Step 3  │     │  │        [标准1] [标准2] [标准3] (横向排列)            │ │
│     │ 正确连接│     │  │           (references的name)                        │ │
│     │ 线路与信│     │  │                                                     │ │
│     │ 号传输  │     │  └─────────────────────────────────────────────────────┘ │
│     └─────────┘     │                                                           │
│          ↓          │                                                           │
│     ┌─────────┐     │                                                           │
│     │ Step 4  │     │                                                           │
│     │ 上位机参│     │                                                           │
│     │ 数配置  │     │                                                           │
│     └─────────┘     │                                                           │
│          ↓          │                                                           │
│     ┌─────────┐     │                                                           │
│     │ Step 5  │     │                                                           │
│     │ 帧原始数│     │                                                           │
│     │ 据获取  │     │                                                           │
│     └─────────┘     │                                                           │
│          ↓          │                                                           │
│     ┌─────────┐     │                                                           │
│     │ Step 6  │     │                                                           │
│     │ 完成调试│     │                                                           │
│     │ 后整理  │     │                                                           │
│     └─────────┘     │                                                           │
│                     │                                                           │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

## 主要功能

### 1. 流程选择
- 界面顶部提供流程选择按钮
- 支持的流程类型：
  - 毫米波雷达调试与测试操作流程（6个步骤）
  - 激光雷达标定流程（6个步骤）
  - 摄像头标定流程（4个步骤）

### 2. 流程图展示
- **左侧1/4区域**：显示流程图
- 流程图为垂直向下布局，带有箭头连接
- 最多显示6个步骤
- 每个步骤显示对应的title文字
- 当前步骤会有颜色填充（绿色高亮）

### 3. 步骤切换
- 点击流程图中的任意步骤框即可切换到该步骤
- 无需额外的切换按钮
- 切换时右侧内容会同步更新

### 4. 内容展示
- **右侧上半部分**：显示当前步骤的图片
- **右侧下半部分**：
  - **上层2/3**：显示当前步骤的description（操作说明）
  - **下层1/3**：显示当前步骤的references的name（相关标准），横向排列

### 5. 自适应布局
- 界面支持窗口大小变化的自适应调整
- 图片会根据容器大小自动缩放
- 文字内容支持滚动显示

## 使用方法

### 新的使用方式（推荐）

1. **启动程序**：运行主程序
2. **进入装配分类**：在导航栏中点击"装配"
3. **点击对应卡片**：
   - 点击"毫米波雷达"卡片 → 自动显示毫米波雷达调试与测试操作流程
   - 点击"激光雷达"卡片 → 自动显示激光雷达标定流程
   - 点击"摄像头"卡片 → 自动显示摄像头标定流程
4. **查看步骤**：点击左侧流程图中的任意步骤查看详细信息
5. **查看标准**：点击右下角的标准按钮可以打开相关文档（如果配置了URL）

### 配置说明

在 `apps_config.json` 中的配置示例：
```json
{
    "name": "毫米波雷达",
    "icon": "../image/radarx.png",
    "path": "./flow.py",
    "description": "毫米波雷达的装配"
}
```

- `name`：卡片显示名称，同时作为流程识别参数
- `path`：设置为 `"./flow.py"` 即可触发流程展示
- 系统会根据 `name` 自动映射到对应的流程：
  - "毫米波雷达" → "毫米波雷达调试与测试操作流程"
  - "激光雷达" → "激光雷达标定流程"
  - "摄像头" → "摄像头标定流程"

## 数据来源

所有流程数据来自 `tac.json` 文件中的 `processes` 部分，包括：
- `title`：步骤标题
- `description`：步骤描述
- `image`：步骤图片路径
- `references`：相关标准信息

## 技术特点

- 基于PyQt5开发
- 支持动态数据加载
- 响应式界面设计
- 模块化代码结构
- 错误处理机制完善

## 文件结构

- `flow.py`：主要的流程展示组件
- `tac.json`：流程数据配置文件
- `../image/`：图片资源目录
- `main_window.py`：主窗口集成代码
