#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的颜色设置方法
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_new_method():
    """测试新的颜色设置方法"""
    print("=== 测试新的颜色设置方法 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(2000)
        app.processEvents()
        
        print(f"分类列表项目数量: {window.category_list.count()}")
        
        # 检查每个分类的颜色
        print("\n=== 检查新方法设置的颜色 ===")
        
        color_found = False
        
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            category_name = item.text()
            
            # 获取背景颜色
            background = item.background()
            color = background.color()
            r, g, b, a = color.red(), color.green(), color.blue(), color.alpha()
            
            print(f"{category_name}: RGB({r}, {g}, {b}, alpha={a})")
            
            if a > 0 and (r > 0 or g > 0 or b > 0):
                color_found = True
                if b > 200:  # 蓝色
                    print(f"  -> 🔵 蓝色系 ✓")
                elif g > 150:  # 绿色
                    print(f"  -> 🟢 绿色系 ✓")
                elif r > 200:  # 橙色/红色
                    print(f"  -> 🟠 橙色系 ✓")
                else:
                    print(f"  -> ⚪ 其他颜色 ✓")
            else:
                print(f"  -> ❌ 无颜色")
        
        print(f"\n=== 结果 ===")
        if color_found:
            print("🎉 新方法成功！颜色已正确设置！")
            print("现在您应该能看到侧边栏有明显的颜色分区了！")
        else:
            print("❌ 新方法失败，仍然没有颜色")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return color_found
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_new_method()
