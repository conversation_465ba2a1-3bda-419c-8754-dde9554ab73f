#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的列数规则测试脚本
"""

import sys
import os

# 添加主程序路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_adaptive_constants():
    """测试自适应常量"""
    try:
        from main_window import AdaptiveConstants
        
        print("=== 自适应常量测试 ===")
        print(f"最大化窗口列数: {AdaptiveConstants.COLS_MAXIMIZED}")
        print(f"恢复窗口列数: {AdaptiveConstants.COLS_RESTORED}")
        
        # 验证列数设置
        assert AdaptiveConstants.COLS_MAXIMIZED == 6, f"最大化列数应为6，实际为{AdaptiveConstants.COLS_MAXIMIZED}"
        assert AdaptiveConstants.COLS_RESTORED == 4, f"恢复窗口列数应为4，实际为{AdaptiveConstants.COLS_RESTORED}"
        
        print("✓ 自适应常量测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 自适应常量测试失败: {e}")
        return False

def test_adaptive_manager():
    """测试自适应管理器"""
    try:
        from main_window import AdaptiveLayoutManager
        
        print("\n=== 自适应管理器测试 ===")
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.is_maximized = False
                
        mock_window = MockMainWindow()
        manager = AdaptiveLayoutManager(mock_window)
        
        # 测试恢复窗口状态
        manager.update_window_state(False)
        cols_restored = manager.get_max_cols()
        print(f"恢复窗口列数: {cols_restored}")
        
        # 测试最大化窗口状态
        manager.update_window_state(True)
        cols_maximized = manager.get_max_cols()
        print(f"最大化窗口列数: {cols_maximized}")
        
        # 验证结果
        assert cols_restored == 4, f"恢复窗口列数应为4，实际为{cols_restored}"
        assert cols_maximized == 6, f"最大化窗口列数应为6，实际为{cols_maximized}"
        
        print("✓ 自适应管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 自适应管理器测试失败: {e}")
        return False

def test_layout_factory():
    """测试布局工厂"""
    try:
        from PyQt5.QtWidgets import QApplication
        from main_window import LayoutFactory, AdaptiveLayoutManager

        print("\n=== 布局工厂测试 ===")

        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # 创建模拟主窗口和自适应管理器
        class MockMainWindow:
            def __init__(self):
                self.is_maximized = False
                self.adaptive_manager = AdaptiveLayoutManager(self)

        mock_window = MockMainWindow()

        # 测试恢复窗口状态
        mock_window.adaptive_manager.update_window_state(False)

        # 模拟应用数据
        mock_apps = [f"App{i}" for i in range(10)]

        # 测试create_grid_with_apps（不传递max_cols参数）
        grid_widget = LayoutFactory.create_grid_with_apps(mock_apps, None, mock_window)
        print("✓ create_grid_with_apps 测试通过（恢复窗口）")

        # 测试create_smart_cards（不传递max_cols参数）
        smart_widget = LayoutFactory.create_smart_cards(mock_apps, None, None, mock_window)
        print("✓ create_smart_cards 测试通过（恢复窗口）")

        # 测试最大化窗口状态
        mock_window.adaptive_manager.update_window_state(True)

        grid_widget_max = LayoutFactory.create_grid_with_apps(mock_apps, None, mock_window)
        print("✓ create_grid_with_apps 测试通过（最大化窗口）")

        smart_widget_max = LayoutFactory.create_smart_cards(mock_apps, None, None, mock_window)
        print("✓ create_smart_cards 测试通过（最大化窗口）")

        print("✓ 布局工厂测试通过")
        return True

    except Exception as e:
        print(f"✗ 布局工厂测试失败: {e}")
        return False

def test_smart_card_container():
    """测试智能卡片容器"""
    try:
        from PyQt5.QtWidgets import QApplication
        from main_window import SmartCardContainer, AdaptiveLayoutManager

        print("\n=== 智能卡片容器测试 ===")

        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # 创建模拟主窗口和自适应管理器
        class MockMainWindow:
            def __init__(self):
                self.is_maximized = False
                self.adaptive_manager = AdaptiveLayoutManager(self)

        mock_window = MockMainWindow()
        mock_apps = [f"App{i}" for i in range(8)]

        # 创建智能卡片容器
        container = SmartCardContainer(mock_apps, 4, 4, mock_window)
        container.adaptive_manager = mock_window.adaptive_manager

        # 测试恢复窗口状态
        mock_window.adaptive_manager.update_window_state(False)
        container.update_adaptive_layout()
        print(f"恢复窗口 - 容器列数: {container.max_cols}, 每页卡片数: {container.cards_per_page}")

        # 测试最大化窗口状态
        mock_window.adaptive_manager.update_window_state(True)
        container.update_adaptive_layout()
        print(f"最大化窗口 - 容器列数: {container.max_cols}, 每页卡片数: {container.cards_per_page}")

        print("✓ 智能卡片容器测试通过")
        return True

    except Exception as e:
        print(f"✗ 智能卡片容器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始全局列数规则测试...\n")
    
    tests = [
        test_adaptive_constants,
        test_adaptive_manager,
        test_layout_factory,
        test_smart_card_container
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！全局列数规则已正确实施")
        print("规则：恢复窗口=4列，最大化窗口=6列")
    else:
        print("❌ 部分测试失败，需要检查实现")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
