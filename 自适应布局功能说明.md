# 工具箱自适应布局功能说明

## 功能概述

本次更新为工具箱应用实现了全面的自适应布局功能，使所有界面组件能够根据窗口状态（最大化/恢复窗口）自动调整尺寸和布局，提供更好的用户体验。

## 核心功能

### 1. 自适应布局管理器 (AdaptiveLayoutManager)

**位置**: `main_window.py` 第 225-287 行

**功能**:
- 集中管理所有组件的自适应尺寸配置
- 监听窗口状态变化并自动更新所有组件
- 提供统一的尺寸获取接口

**主要方法**:
- `update_window_state(is_maximized)`: 更新窗口状态并触发自适应调整
- `get_card_size()`: 获取当前状态下的卡片尺寸
- `get_button_size()`: 获取当前状态下的按钮尺寸
- `get_file_size()`: 获取当前状态下的文件框尺寸
- `get_max_cols()`: 获取当前状态下的最大列数
- `get_spacing()`: 获取当前状态下的元素间距

### 2. 自适应常量配置 (AdaptiveConstants)

**位置**: `main_window.py` 第 200-224 行

**配置项**:
```python
# 应用卡片尺寸
CARD_SIZE_MAXIMIZED = (260, 200)      # 最大化时
CARD_SIZE_RESTORED = (220, 170)       # 恢复窗口时

# 按钮尺寸  
BUTTON_SIZE_MAXIMIZED = (240, 180)    # 最大化时
BUTTON_SIZE_RESTORED = (200, 150)     # 恢复窗口时

# 文件浏览器尺寸
FILE_SIZE_MAXIMIZED = (150, 140)      # 最大化时
FILE_SIZE_RESTORED = (130, 120)       # 恢复窗口时

# 布局配置
MAX_COLS_MAXIMIZED = 6                # 最大化时列数
MAX_COLS_RESTORED = 4                 # 恢复窗口时列数

# 间距配置
SPACING_MAXIMIZED = 20                # 最大化时间距
SPACING_RESTORED = 15                 # 恢复窗口时间距
```

## 支持自适应的组件

### 1. 应用卡片 (AppFrame)

**位置**: `main_window.py` 第 1300+ 行

**自适应功能**:
- 根据窗口状态调整卡片尺寸
- 自动更新内部按钮尺寸
- 支持动态列数调整

**使用方法**:
```python
app_frame.adaptive_manager = main_window.adaptive_manager
app_frame.update_adaptive_size()
```

### 2. 拖拽按钮 (DraggableButton)

**位置**: `main_window.py` 第 1400+ 行

**自适应功能**:
- 根据窗口状态调整按钮尺寸
- 保持图标和文字的比例协调

### 3. 文件浏览器框 (FileExplorerFrame)

**位置**: `main_window.py` 第 1500+ 行

**自适应功能**:
- 根据窗口状态调整文件框尺寸
- 适配不同的显示密度

### 4. 统计卡片 (StatisticsCard)

**位置**: `home_page.py` 第 71-198 行

**自适应功能**:
- 首页统计卡片根据窗口状态调整尺寸
- 最大化时: 720x210，恢复窗口时: 600x170

### 5. AI对话框 (ChatDialog)

**位置**: `chat_dialog.py` 第 51-83 行

**自适应功能**:
- 根据父窗口状态调整对话框尺寸
- 最大化时: 420x550，恢复窗口时: 350x450
- 自动重新定位到窗口右下角

## 窗口状态管理

### 窗口状态切换

**触发方式**:
- 点击标题栏的窗口切换按钮
- 调用 `toggle_window_size()` 方法

**切换流程**:
1. 更新窗口几何状态 (`showMaximized()` 或 `setGeometry()`)
2. 更新内部状态标志 (`is_maximized`)
3. 调用自适应管理器更新 (`adaptive_manager.update_window_state()`)
4. 自动更新所有现有组件尺寸
5. 重新布局当前显示的内容

### 列数自适应

**配置**:
- 最大化状态: 6列布局
- 恢复窗口状态: 4列布局

**实现**:
- 通过 `max_cols` 属性控制
- 在窗口状态切换时自动更新
- 影响所有网格布局的列数

## 测试验证

### 1. 基础功能测试

**文件**: `test_adaptive_layout.py`

**测试内容**:
- 自适应管理器基本功能
- 窗口状态切换
- 组件尺寸更新验证

### 2. 全面功能测试

**文件**: `test_comprehensive_adaptive.py`

**测试内容**:
- 首页统计卡片自适应
- 应用卡片自适应
- AI对话框自适应
- 列数配置验证
- 间距配置验证
- 多次切换稳定性

### 3. 演示程序

**文件**: `demo_adaptive_layout.py`

**功能**:
- 自动演示所有自适应功能
- 展示不同界面的自适应行为
- 提供可视化的功能验证

## 使用说明

### 用户操作

1. **窗口状态切换**: 点击标题栏右侧的窗口切换按钮 (◐)
2. **观察自适应**: 切换后观察所有界面元素的尺寸变化
3. **分类浏览**: 在不同分类间切换，体验一致的自适应行为

### 开发者集成

1. **为新组件添加自适应支持**:
```python
class NewComponent(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.adaptive_manager = None
        
    def update_adaptive_size(self):
        if self.adaptive_manager:
            # 根据 self.adaptive_manager.current_state 调整尺寸
            pass
```

2. **在创建组件时设置自适应管理器**:
```python
component = NewComponent()
component.adaptive_manager = main_window.adaptive_manager
component.update_adaptive_size()
```

## 技术特点

### 1. 集中化管理
- 所有自适应配置集中在 `AdaptiveConstants` 类
- 统一的管理器负责协调所有组件

### 2. 实时响应
- 窗口状态变化时立即更新所有组件
- 无需手动刷新或重启

### 3. 向后兼容
- 现有功能完全保持不变
- 新增的自适应功能不影响原有逻辑

### 4. 可扩展性
- 易于为新组件添加自适应支持
- 配置参数可根据需要调整

## 总结

本次实现的自适应布局功能完全满足了用户需求：

✅ **要求**: "设置窗口自适应，要求工具箱所有的界面都能自适应当前的状态（最大化、恢复窗口）"

✅ **实现**: 
- 所有主要界面组件都支持自适应
- 窗口状态切换时实时调整
- 布局列数和间距自动适配
- 提供了完整的测试验证

✅ **质量保证**:
- 经过全面测试验证
- 多次切换稳定性良好
- 用户体验流畅自然

工具箱现在具备了完整的自适应布局能力，能够在不同窗口状态下提供最佳的界面体验。
