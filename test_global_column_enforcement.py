#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局列数规则测试脚本
测试恢复窗口=4列，最大化窗口=6列的全局规则是否正确实施
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加主程序路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow, SmartCardContainer, LayoutFactory
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class ColumnTestWindow(QWidget):
    """列数测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.test_results = []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("全局列数规则测试")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("全局列数规则测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("测试规则：恢复窗口=4列，最大化窗口=6列")
        info.setFont(QFont("Microsoft YaHei", 12))
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_test_btn = QPushButton("开始测试")
        self.start_test_btn.clicked.connect(self.start_test)
        button_layout.addWidget(self.start_test_btn)
        
        self.toggle_window_btn = QPushButton("切换窗口状态")
        self.toggle_window_btn.clicked.connect(self.toggle_window_state)
        self.toggle_window_btn.setEnabled(False)
        button_layout.addWidget(self.toggle_window_btn)
        
        self.check_columns_btn = QPushButton("检查列数")
        self.check_columns_btn.clicked.connect(self.check_columns)
        self.check_columns_btn.setEnabled(False)
        button_layout.addWidget(self.check_columns_btn)
        
        layout.addLayout(button_layout)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
        
    def start_test(self):
        """开始测试"""
        try:
            self.log("开始启动主窗口...")
            self.main_window = MainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTimer.singleShot(2000, self.enable_test_buttons)
            
            self.log("主窗口已启动，等待2秒后开始测试...")
            
        except Exception as e:
            self.log(f"启动主窗口失败: {e}")
            
    def enable_test_buttons(self):
        """启用测试按钮"""
        self.toggle_window_btn.setEnabled(True)
        self.check_columns_btn.setEnabled(True)
        self.start_test_btn.setEnabled(False)
        
        # 自动开始第一次检查
        self.check_columns()
        
    def toggle_window_state(self):
        """切换窗口状态"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 使用主窗口的切换方法
            self.main_window.toggle_window_size()
            
            # 等待状态更新后检查
            QTimer.singleShot(1000, self.check_columns)
            
        except Exception as e:
            self.log(f"切换窗口状态失败: {e}")
            
    def check_columns(self):
        """检查当前列数设置"""
        if not self.main_window:
            self.log("主窗口未启动")
            return
            
        try:
            # 检查窗口状态
            is_maximized = self.main_window.isMaximized()
            window_state = "最大化" if is_maximized else "恢复窗口"
            
            # 检查自适应管理器的列数
            adaptive_cols = self.main_window.adaptive_manager.get_max_cols()
            
            # 检查主窗口的max_cols属性
            main_window_cols = getattr(self.main_window, 'max_cols', None)
            
            # 预期的列数
            expected_cols = 6 if is_maximized else 4
            
            self.log(f"\n=== 列数检查结果 ===")
            self.log(f"窗口状态: {window_state}")
            self.log(f"预期列数: {expected_cols}")
            self.log(f"自适应管理器列数: {adaptive_cols}")
            self.log(f"主窗口max_cols: {main_window_cols}")
            
            # 验证结果
            adaptive_correct = adaptive_cols == expected_cols
            main_window_correct = main_window_cols == expected_cols
            
            self.log(f"自适应管理器正确: {'✓' if adaptive_correct else '✗'}")
            self.log(f"主窗口属性正确: {'✓' if main_window_correct else '✗'}")
            
            # 检查实际的布局组件
            self.check_layout_components(expected_cols)
            
            # 记录测试结果
            result = {
                'window_state': window_state,
                'expected': expected_cols,
                'adaptive_manager': adaptive_cols,
                'main_window': main_window_cols,
                'adaptive_correct': adaptive_correct,
                'main_window_correct': main_window_correct
            }
            self.test_results.append(result)
            
        except Exception as e:
            self.log(f"检查列数失败: {e}")
            
    def check_layout_components(self, expected_cols):
        """检查布局组件的实际列数使用情况"""
        try:
            # 检查SmartCardContainer
            if hasattr(self.main_window, 'scroll_content'):
                smart_containers = self.main_window.scroll_content.findChildren(SmartCardContainer)
                self.log(f"找到 {len(smart_containers)} 个智能卡片容器")
                
                for i, container in enumerate(smart_containers):
                    container_cols = container.max_cols
                    correct = container_cols == expected_cols
                    self.log(f"  容器{i+1}: {container_cols}列 {'✓' if correct else '✗'}")
                    
        except Exception as e:
            self.log(f"检查布局组件失败: {e}")
            
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.result_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.result_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    test_window = ColumnTestWindow()
    test_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
