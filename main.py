import sys
import os
import ctypes
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QDialog, QVBoxLayout, QHBoxLayout, QComboBox, QPushButton, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QIcon, QFont
from main_window import MainWindow

class VehicleSelectionDialog(QDialog):
    """车型选择对话框"""

    def __init__(self):
        super().__init__()
        self.selected_vehicle = None
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("传感器工具箱 - 车型选择")
        self.setFixedSize(400, 400)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 设置窗口图标
        icon_path = "../image/start.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            png_icon_path = "../image/start.png"
            if os.path.exists(png_icon_path):
                self.setWindowIcon(QIcon(png_icon_path))

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # 标题标签
        title_label = QLabel("请选择车型")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Microsoft YaHei", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)

        # 车型选择下拉框
        self.vehicle_combo = QComboBox()
        self.vehicle_combo.addItems(["宝马X6", "奥迪A8", "长安深蓝SL03"])
        self.vehicle_combo.setCurrentIndex(0)  # 默认选择第一个
        self.vehicle_combo.setStyleSheet("""
            QComboBox {
                font-size: 22px;
                font-family: Microsoft YaHei;
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 10px;
            }
        """)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 确认按钮
        confirm_btn = QPushButton("确认启动")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 22px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        confirm_btn.clicked.connect(self.accept_selection)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 22px;
                font-family: Microsoft YaHei;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)

        # 添加到主布局
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.vehicle_combo)
        main_layout.addStretch()
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
            }
        """)

    def accept_selection(self):
        """确认选择"""
        self.selected_vehicle = self.vehicle_combo.currentText()
        self.accept()

    def get_selected_vehicle(self):
        """获取选择的车型"""
        return self.selected_vehicle

def main():
    # 确保当前工作目录是程序所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)

    # 设置应用程序用户模型ID，让Windows识别这是一个独立的应用程序
    # 这样可以避免任务栏显示Python图标
    try:
        # 设置应用程序ID，这会让Windows将此应用视为独立应用而不是Python脚本
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("SensorToolbox.Main.1.0")
    except:
        pass  # 如果设置失败，继续运行

    app = QApplication(sys.argv)

    # 设置应用程序图标（这会影响任务栏图标）
    icon_path = "../image/start.ico"
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    else:
        # 如果ico文件不存在，尝试使用png文件
        png_icon_path = "../image/start.png"
        if os.path.exists(png_icon_path):
            app.setWindowIcon(QIcon(png_icon_path))

    # 临时禁用车型选择界面 - 直接启动主窗口
    # TODO: 后续需要时取消注释以下代码并注释掉直接启动部分
    #"""
    # 显示车型选择对话框
    vehicle_dialog = VehicleSelectionDialog()
    if vehicle_dialog.exec_() == QDialog.Accepted:
        selected_vehicle = vehicle_dialog.get_selected_vehicle()
        print(f"用户选择的车型: {selected_vehicle}")  # 可以在这里记录选择的车型

        # 创建并显示主窗口
        window = MainWindow()
        window.show()

        # 使用QTimer延迟调用，确保主窗口完全加载后再显示首页
        QTimer.singleShot(100, window.show_home_page)

        sys.exit(app.exec_())
    else:
        # 用户取消了选择，退出程序
        sys.exit(0)
    #"""

    # 直接启动主窗口（临时方案）
    window = MainWindow()
    window.show()

    # 使用QTimer延迟调用，确保主窗口完全加载后再显示基础检查界面
    # 增加延迟时间，确保界面完全初始化
    QTimer.singleShot(800, lambda: window.show_category_by_name("基础检查"))

    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 