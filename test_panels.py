#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的基础检查三栏面板功能
"""

import sys
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from main_window import MainWindow

def test_basic_check_panels():
    """测试基础检查面板功能"""
    print("开始测试基础检查三栏面板功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    def run_tests():
        """运行测试"""
        print("1. 测试导航到基础检查...")
        
        # 导航到基础检查
        window.show_category_by_name("基础检查")
        
        # 等待界面加载
        QTimer.singleShot(1000, test_panel_data)
    
    def test_panel_data():
        """测试面板数据显示"""
        print("2. 测试面板数据显示...")
        
        # 查找面板容器
        from PyQt5.QtWidgets import QWidget
        panels_container = window.scroll_content.findChild(QWidget, "basic_check_panels")
        if panels_container:
            print("✓ 找到面板容器")
            
            # 检查是否有三个文本框
            if hasattr(window, 'check_items_text'):
                print("✓ 检查项目面板存在")
                print(f"  内容预览: {window.check_items_text.toPlainText()[:50]}...")
            
            if hasattr(window, 'check_content_text'):
                print("✓ 检查内容面板存在")
                print(f"  内容预览: {window.check_content_text.toPlainText()[:50]}...")
            
            if hasattr(window, 'check_standards_text'):
                print("✓ 国家标准面板存在")
                print(f"  内容预览: {window.check_standards_text.toPlainText()[:50]}...")
        else:
            print("✗ 未找到面板容器")
        
        # 测试点击卡片
        QTimer.singleShot(1000, test_card_click)
    
    def test_card_click():
        """测试点击卡片功能"""
        print("3. 测试点击卡片功能...")
        
        try:
            # 模拟点击第一个基础检查卡片
            basic_check_data = window.apps_data.get("基础检查", [])
            if basic_check_data:
                first_item = basic_check_data[0]
                print(f"模拟点击卡片: {first_item.get('name', '未知')}")
                
                # 调用显示面板数据的方法
                window.show_basic_check_table(first_item)
                
                # 检查数据是否更新
                QTimer.singleShot(500, check_updated_data)
            else:
                print("✗ 未找到基础检查数据")
                finish_test()
        except Exception as e:
            print(f"✗ 测试点击卡片时出错: {e}")
            finish_test()
    
    def check_updated_data():
        """检查更新后的数据"""
        print("4. 检查数据更新...")
        
        try:
            if hasattr(window, 'check_items_text'):
                content = window.check_items_text.toPlainText()
                if "安全防护检查" in content:
                    print("✓ 检查项目数据已更新")
                else:
                    print("✗ 检查项目数据未更新")
            
            if hasattr(window, 'check_content_text'):
                content = window.check_content_text.toPlainText()
                if "外观无裂纹无破损" in content:
                    print("✓ 检查内容数据已更新")
                else:
                    print("✗ 检查内容数据未更新")
            
            if hasattr(window, 'check_standards_text'):
                content = window.check_standards_text.toPlainText()
                if "GB2811-2019" in content:
                    print("✓ 国家标准数据已更新")
                else:
                    print("✗ 国家标准数据未更新")
                    
        except Exception as e:
            print(f"✗ 检查数据更新时出错: {e}")
        
        finish_test()
    
    def finish_test():
        """完成测试"""
        print("\n测试完成！")
        print("新的三栏面板功能已成功实现，完全借鉴了chat_robot_widget.py的设计")
        print("- ✓ 三栏布局：检查项目、检查内容、国家标准")
        print("- ✓ 美观的面板样式，栏与栏之间有间隙")
        print("- ✓ 标题+文本框内容的呈现方式")
        print("- ✓ 数据格式兼容现有JSON结构")
        app.quit()
    
    # 延迟启动测试，确保窗口完全加载
    QTimer.singleShot(2000, run_tests)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_basic_check_panels()
