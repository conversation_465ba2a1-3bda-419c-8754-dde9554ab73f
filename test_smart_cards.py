#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能卡片容器功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_smart_card_container():
    """测试智能卡片容器的动态切换功能"""
    print("=== 测试智能卡片容器功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面初始化
        QTest.qWait(1000)
        
        # 设置当前分类为基础检查
        window.current_category = "基础检查"
        window.display_single_category("基础检查")
        
        print("✓ 切换到基础检查分类")
        
        # 等待界面更新
        QTest.qWait(1000)
        
        # 检查基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"基础检查项目数量: {len(basic_check_data)}")
        
        if len(basic_check_data) > 0:
            # 查找智能卡片容器
            from main_window import SmartCardContainer
            smart_containers = window.scroll_content.findChildren(SmartCardContainer)
            
            if smart_containers:
                smart_container = smart_containers[0]
                print("✓ 找到智能卡片容器")
                print(f"  当前显示模式: {smart_container.display_mode}")
                print(f"  总页数: {smart_container.total_pages}")
                print(f"  每页卡片数: {smart_container.cards_per_page}")
                
                # 测试初始状态（应该是网格模式）
                if smart_container.display_mode == 'grid':
                    print("✓ 初始状态为网格模式")
                    
                    # 查找第一个卡片并模拟点击
                    from main_window import AppFrame
                    app_frames = smart_container.findChildren(AppFrame)
                    
                    if app_frames:
                        first_card = app_frames[0]
                        print(f"✓ 找到第一个卡片: {first_card.app_data.get('name', '未知')}")
                        
                        # 模拟点击卡片
                        print("模拟点击第一个卡片...")
                        first_card.handle_single_click()
                        
                        # 等待模式切换
                        QTest.qWait(500)
                        
                        # 检查是否切换到滑动模式
                        if smart_container.display_mode == 'sliding':
                            print("✓ 成功切换到滑动模式")
                            print(f"  当前页: {smart_container.current_page + 1}")
                            print(f"  选中的应用: {smart_container.selected_app.get('name', '未知') if smart_container.selected_app else '无'}")
                            
                            # 滑动模式已激活，无需额外的控制按钮
                            print("✓ 滑动模式已激活，界面简洁无额外控制元素")
                            
                            # 测试滑动翻页功能
                            if smart_container.total_pages > 1:
                                print("\n测试滑动翻页功能...")
                                original_page = smart_container.current_page

                                # 直接调用翻页方法测试
                                smart_container.next_page()

                                QTest.qWait(200)

                                if smart_container.current_page != original_page:
                                    print(f"✓ 滑动翻页成功: 从第{original_page + 1}页切换到第{smart_container.current_page + 1}页")
                                else:
                                    print("✗ 滑动翻页失败")

                            print("✓ 滑动模式功能测试完成，界面保持简洁")
                        else:
                            print("✗ 未能切换到滑动模式")
                    else:
                        print("✗ 未找到卡片")
                else:
                    print(f"✗ 初始状态不是网格模式，当前模式: {smart_container.display_mode}")
            else:
                print("✗ 未找到智能卡片容器")
        else:
            print("✗ 没有基础检查数据")
        
        print("\n=== 测试完成 ===")
        
        # 保持窗口打开一段时间以便观察
        QTimer.singleShot(8000, app.quit)
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始智能卡片容器功能测试...\n")
    
    success = test_smart_card_container()
    
    if success:
        print("\n✓ 智能卡片容器测试完成!")
    else:
        print("\n✗ 智能卡片容器测试失败!")

if __name__ == "__main__":
    main()
