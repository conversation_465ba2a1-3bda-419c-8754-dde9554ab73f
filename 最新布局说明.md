# 流程展示功能 - 最新布局说明

## 布局变更概述

根据最新需求，界面布局已从左右分割改为上下分割的新设计：

### 新界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                                                                 │
│                              上半部分：图片区域                                  │
│                           (整个界面上半部分全部是图片)                           │
│                              (step的image路径)                                  │
│                                                                                 │
│                                                                                 │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                           │
│    左下 1/4 宽度    │                   右下 3/4 宽度                          │
│   ┌─────────────┐   │  ┌─────────────────────────────────────────────────────┐ │
│   │   Step 1    │   │  │                                                     │ │
│   │   流程步骤1  │   │  │              文字解释区域 (2/3 高度)                │ │
│   └─────────────┘   │  │            (step的description内容)                  │ │
│          ⬇          │  │                                                     │ │
│   ┌─────────────┐   │  │                                                     │ │
│   │   Step 2    │   │  │                                                     │ │
│   │   流程步骤2  │   │  ├─────────────────────────────────────────────────────┤ │
│   └─────────────┘   │  │                                                     │ │
│          ⬇          │  │              标准区域 (1/3 高度)                    │ │
│   ┌─────────────┐   │  │        [标准1] [标准2] [标准3] (横向排列)            │ │
│   │   Step 3    │   │  │           (references的name)                        │ │
│   │   流程步骤3  │   │  │                                                     │ │
│   └─────────────┘   │  └─────────────────────────────────────────────────────┘ │
│                     │                                                           │
│  (最多显示3个步骤)   │                                                           │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

## 主要特点

### ✅ 布局优势
- **图片展示更突出**: 整个上半部分都是图片，视觉冲击力更强
- **流程图更紧凑**: 只显示前3步，避免冗余，界面更简洁
- **内容区域更宽**: 右下角有更多空间显示文字和标准
- **视觉层次清晰**: 上下分层，左右分区，层次分明

### 🎯 尺寸比例
- **垂直比例**: 上半部分图片 50% : 下半部分内容 50%
- **下半部分水平比例**: 左下流程图 25% : 右下内容 75%
- **右下内容垂直比例**: 文字解释 66.7% : 标准区域 33.3%

### 🔧 功能限制
- **流程步骤**: 最多显示3个步骤（原来是6个）
- **步骤切换**: 只能在前3个步骤之间切换
- **箭头样式**: 使用更明显的蓝色⬇符号

## 使用方式

### 启动流程
1. **运行主程序**: `python main.py`
2. **进入装配分类**: 点击导航栏"装配"
3. **选择流程**: 点击对应卡片
   - "毫米波雷达" → 毫米波雷达调试与测试操作流程
   - "激光雷达" → 激光雷达标定流程
   - "摄像头" → 摄像头标定流程

### 交互操作
- **步骤切换**: 点击左下角任意步骤框
- **内容查看**: 右下角显示对应的文字说明和标准
- **图片查看**: 上半部分显示对应的步骤图片
- **标准访问**: 点击标准按钮可打开相关文档

## 技术实现

### 主要代码修改

1. **主布局结构** (`setup_ui`):
   ```python
   # 从水平布局改为垂直布局
   main_layout = QVBoxLayout(self)
   # 上半部分：图片
   self.setup_image_area(main_layout)
   # 下半部分：流程图+内容
   bottom_container = QWidget()
   ```

2. **流程步骤限制** (`create_flow_chart`):
   ```python
   # 最多显示3个步骤
   max_steps = min(3, len(self.steps_data))
   ```

3. **图片区域独立** (`setup_image_area`):
   ```python
   # 图片区域成为独立的上半部分
   self.image_label.setMinimumHeight(300)
   ```

### 配置支持

在 `apps_config.json` 中的配置：
```json
{
    "name": "毫米波雷达",
    "icon": "../image/radarx.png",
    "path": "./flow.py",
    "description": "毫米波雷达的装配"
}
```

### 参数传递机制
```
卡片点击 → launch_app() → show_flow_page(process_name) → FlowWidget(parent, process_name)
```

## 测试验证

### 测试方法
1. **独立测试**: `python test_flow_integration.py`
2. **主程序测试**: `python main.py` → 装配 → 点击卡片

### 验证要点
- ✅ 上半部分图片正确显示
- ✅ 左下角显示3个流程步骤
- ✅ 右下角文字和标准正确显示
- ✅ 步骤切换功能正常
- ✅ 箭头样式明显可见
- ✅ 自适应窗口变化

## 文件清单

### 主要文件
- `flow.py`: 核心流程展示组件
- `main_window.py`: 主窗口集成逻辑
- `tac.json`: 流程数据配置
- `apps_config.json`: 应用卡片配置

### 测试文件
- `test_flow_integration.py`: 集成测试界面
- `create_test_images.py`: 测试图片生成
- `../image/step*.png`: 测试图片文件

### 文档文件
- `新布局实现说明.md`: 详细技术实现
- `流程展示功能修改总结.md`: 修改历史总结
- `最新布局说明.md`: 当前文档

新布局已完全实现，现在可以享受更加直观和紧凑的流程展示体验！
