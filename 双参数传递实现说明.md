# 双参数传递功能实现说明

## 功能概述

已成功实现双参数传递功能，解决了毫米波雷达在不同分类（装配、调试、标定、测试）中的流程区分问题。

## 参数传递流程

### 1. 参数来源
```json
// apps_config.json中的配置
"装配": [
    {
        "name": "毫米波雷达",
        "icon": "../image/radarx.png",
        "path": "./flow.py",
        "description": "毫米波雷达的装配"
    }
]
```

### 2. 传递链路
```
点击卡片 → launch_app() → get_current_category() + app.name 
→ show_flow_page(category, device_name) → FlowWidget(parent, category, device_name)
→ build_process_name() → 匹配tac.json中的流程
```

### 3. 参数映射规则

#### 毫米波雷达映射
| 分类 | 设备名称 | 最终流程名称 |
|------|----------|-------------|
| 装配 | 毫米波雷达 | 毫米波雷达装调 |
| 调试 | 毫米波雷达 | 毫米波雷达调试 |
| 标定 | 毫米波雷达 | 毫米波雷达标定 |
| 测试 | 毫米波雷达 | 毫米波雷达测试 |

#### 激光雷达映射
| 分类 | 设备名称 | 最终流程名称 |
|------|----------|-------------|
| 标定 | 激光雷达 | 激光雷达标定流程 |

#### 摄像头映射
| 分类 | 设备名称 | 最终流程名称 |
|------|----------|-------------|
| 标定 | 摄像头 | 摄像头标定流程 |

## 代码实现详情

### 1. FlowWidget构造函数修改
```python
def __init__(self, parent=None, category=None, device_name=None):
    self.category = category  # 分类名称
    self.device_name = device_name  # 设备名称
    
    # 分类到流程后缀的映射
    self.category_mapping = {
        "装配": "装调",
        "调试": "调试", 
        "标定": "标定",
        "测试": "测试"
    }
    
    # 构建目标流程名称
    self.target_process_name = self.build_process_name(category, device_name)
```

### 2. 流程名称构建逻辑
```python
def build_process_name(self, category, device_name):
    if device_name == "毫米波雷达":
        # 毫米波雷达：设备名 + 后缀
        process_suffix = self.category_mapping.get(category, "")
        process_name = f"{device_name}{process_suffix}"
        
    elif device_name == "激光雷达":
        # 激光雷达：特殊命名规则
        if category == "标定":
            process_name = "激光雷达标定流程"
            
    elif device_name == "摄像头":
        # 摄像头：特殊命名规则
        if category == "标定":
            process_name = "摄像头标定流程"
    
    return process_name
```

### 3. 主窗口集成
```python
def launch_app(self, app):
    if path.endswith('flow.py'):
        category = self.get_current_category()  # 获取当前分类
        device_name = app.get('name', '')       # 获取设备名称
        self.show_flow_page(category, device_name)

def get_current_category(self):
    """获取当前选中的分类名称"""
    if self.category_list.currentItem():
        return self.category_list.currentItem().text()
    return None

def show_flow_page(self, category=None, device_name=None):
    from flow import FlowWidget
    self.flow_widget = FlowWidget(self, category, device_name)
    self.scroll_layout.addWidget(self.flow_widget)
```

## 支持的流程组合

### 当前支持的组合
1. **毫米波雷达** (4种流程)
   - 装配 → 毫米波雷达装调
   - 调试 → 毫米波雷达调试
   - 标定 → 毫米波雷达标定
   - 测试 → 毫米波雷达测试

2. **激光雷达** (1种流程)
   - 标定 → 激光雷达标定流程

3. **摄像头** (1种流程)
   - 标定 → 摄像头标定流程

### 扩展支持
如需添加新的设备或流程，只需：
1. 在`tac.json`中添加对应的流程数据
2. 在`build_process_name()`方法中添加映射规则
3. 在`apps_config.json`中添加对应的卡片配置

## 测试验证

### 测试程序
`test_flow_integration.py`提供了完整的测试界面：
- **毫米波雷达装配** → 测试装配流程
- **毫米波雷达调试** → 测试调试流程  
- **激光雷达标定** → 测试标定流程
- **摄像头测试** → 测试摄像头流程

### 验证要点
1. ✅ **参数正确传递**: 分类和设备名称正确传递
2. ✅ **流程名称构建**: 根据规则正确构建流程名称
3. ✅ **流程数据加载**: 成功加载对应的流程数据
4. ✅ **界面正确显示**: 流程图和内容正确显示
5. ✅ **调试信息输出**: 控制台输出详细的调试信息

### 调试信息示例
```
构建流程名称: 分类=装配, 设备=毫米波雷达 → 毫米波雷达装调
启动流程应用: 分类=装配, 设备=毫米波雷达
显示流程页面: 分类=装配, 设备=毫米波雷达
成功加载流程: 毫米波雷达装调
```

## 使用方式

### 在主程序中使用
1. 运行主程序：`python main.py`
2. 点击导航栏选择分类（如"装配"）
3. 点击对应的设备卡片（如"毫米波雷达"）
4. 系统自动传递分类和设备名称
5. 显示对应的流程界面

### 配置要求
确保`apps_config.json`中的配置正确：
```json
{
    "name": "毫米波雷达",  // 设备名称
    "path": "./flow.py",   // 必须是flow.py
    "description": "毫米波雷达的装配"  // 描述
}
```

## 技术优势

### ✅ 精确匹配
- 通过双参数确保流程匹配的准确性
- 避免了单参数可能的歧义问题

### ✅ 扩展性强
- 支持新设备和新流程的轻松添加
- 映射规则清晰，易于维护

### ✅ 调试友好
- 详细的调试信息输出
- 错误处理和提示完善

### ✅ 向后兼容
- 保持原有功能不变
- 新功能不影响现有代码

双参数传递功能已完全实现，现在可以精确区分不同分类下的同名设备流程！🎯
